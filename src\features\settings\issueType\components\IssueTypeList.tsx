"use client";

import { useState } from "react";
import { Input } from "@/components/ui/input";
import { Button } from "@/components/ui/button";
import { Badge } from "@/components/ui/badge";
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select";
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu";
import {
  Search,
  Filter,
  X,
  SortAsc,
  SortDesc,
  RotateCcw,
} from "lucide-react";
import { CategoryFilter, CategorySort } from "@/shared/types/categoryTypes";

interface CategoryFiltersProps {
  filter: CategoryFilter;
  sort: CategorySort;
  onFilterChange: (filter: CategoryFilter) => void;
  onSortChange: (sort: CategorySort) => void;
  onReset: () => void;
  totalCount?: number;
  filteredCount?: number;
}

export default function CategoryFilters({
  filter,
  sort,
  onFilterChange,
  onSortChange,
  onReset,
  totalCount = 0,
  filteredCount = 0,
}: CategoryFiltersProps) {
  const [searchValue, setSearchValue] = useState(filter.search || "");

  const handleSearchChange = (value: string) => {
    setSearchValue(value);
    onFilterChange({ ...filter, search: value || undefined });
  };

  const handleStatusFilter = (isActive?: boolean) => {
    onFilterChange({ ...filter, isActive });
  };

  const handleSortChange = (field: CategorySort["field"]) => {
    const newDirection = 
      sort.field === field && sort.direction === "asc" ? "desc" : "asc";
    onSortChange({ field, direction: newDirection });
  };

  const hasActiveFilters = 
    filter.search || 
    filter.isActive !== undefined || 
    filter.projectId;

  const activeFilterCount = [
    filter.search,
    filter.isActive !== undefined,
    filter.projectId,
  ].filter(Boolean).length;

  return (
    <div className="space-y-4">
      {/* Search và Filter chính */}
      <div className="flex flex-col sm:flex-row gap-4">
        {/* Search */}
        <div className="relative flex-1">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-4 w-4 text-gray-400" />
          <Input
            placeholder="Tìm kiếm category..."
            value={searchValue}
            onChange={(e) => handleSearchChange(e.target.value)}
            className="pl-10"
          />
          {searchValue && (
            <Button
              variant="ghost"
              size="sm"
              onClick={() => handleSearchChange("")}
              className="absolute right-1 top-1/2 transform -translate-y-1/2 h-6 w-6 p-0"
            >
              <X className="h-3 w-3" />
            </Button>
          )}
        </div>

        {/* Status Filter */}
        <Select
          value={
            filter.isActive === undefined 
              ? "all" 
              : filter.isActive 
                ? "active" 
                : "inactive"
          }
          onValueChange={(value) => {
            const isActive = 
              value === "all" ? undefined : value === "active";
            handleStatusFilter(isActive);
          }}
        >
          <SelectTrigger className="w-[180px]">
            <SelectValue placeholder="Trạng thái" />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="all">Tất cả trạng thái</SelectItem>
            <SelectItem value="active">Đang hoạt động</SelectItem>
            <SelectItem value="inactive">Vô hiệu hóa</SelectItem>
          </SelectContent>
        </Select>

        {/* Sort Dropdown */}
        <DropdownMenu>
          <DropdownMenuTrigger asChild>
            <Button variant="outline" className="gap-2">
              {sort.direction === "asc" ? (
                <SortAsc className="h-4 w-4" />
              ) : (
                <SortDesc className="h-4 w-4" />
              )}
              Sắp xếp
            </Button>
          </DropdownMenuTrigger>
          <DropdownMenuContent align="end">
            <DropdownMenuLabel>Sắp xếp theo</DropdownMenuLabel>
            <DropdownMenuSeparator />
            <DropdownMenuItem onClick={() => handleSortChange("order")}>
              <div className="flex items-center justify-between w-full">
                <span>Thứ tự</span>
                {sort.field === "order" && (
                  sort.direction === "asc" ? 
                    <SortAsc className="h-3 w-3" /> : 
                    <SortDesc className="h-3 w-3" />
                )}
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSortChange("name")}>
              <div className="flex items-center justify-between w-full">
                <span>Tên</span>
                {sort.field === "name" && (
                  sort.direction === "asc" ? 
                    <SortAsc className="h-3 w-3" /> : 
                    <SortDesc className="h-3 w-3" />
                )}
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSortChange("issueCount")}>
              <div className="flex items-center justify-between w-full">
                <span>Số lượng issues</span>
                {sort.field === "issueCount" && (
                  sort.direction === "asc" ? 
                    <SortAsc className="h-3 w-3" /> : 
                    <SortDesc className="h-3 w-3" />
                )}
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSortChange("createdAt")}>
              <div className="flex items-center justify-between w-full">
                <span>Ngày tạo</span>
                {sort.field === "createdAt" && (
                  sort.direction === "asc" ? 
                    <SortAsc className="h-3 w-3" /> : 
                    <SortDesc className="h-3 w-3" />
                )}
              </div>
            </DropdownMenuItem>
            <DropdownMenuItem onClick={() => handleSortChange("updatedAt")}>
              <div className="flex items-center justify-between w-full">
                <span>Cập nhật gần nhất</span>
                {sort.field === "updatedAt" && (
                  sort.direction === "asc" ? 
                    <SortAsc className="h-3 w-3" /> : 
                    <SortDesc className="h-3 w-3" />
                )}
              </div>
            </DropdownMenuItem>
          </DropdownMenuContent>
        </DropdownMenu>

        {/* Reset Filters */}
        {hasActiveFilters && (
          <Button
            variant="outline"
            onClick={onReset}
            className="gap-2"
          >
            <RotateCcw className="h-4 w-4" />
            Reset
          </Button>
        )}
      </div>

      {/* Active Filters và Results Count */}
      <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-4">
        {/* Active Filters */}
        <div className="flex flex-wrap items-center gap-2">
          {hasActiveFilters && (
            <>
              <span className="text-sm text-gray-500">Bộ lọc:</span>
              {filter.search && (
                <Badge variant="secondary" className="gap-1">
                  Tìm kiếm: "{filter.search}"
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleSearchChange("")}
                    className="h-3 w-3 p-0 hover:bg-transparent"
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {filter.isActive !== undefined && (
                <Badge variant="secondary" className="gap-1">
                  {filter.isActive ? "Đang hoạt động" : "Vô hiệu hóa"}
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={() => handleStatusFilter(undefined)}
                    className="h-3 w-3 p-0 hover:bg-transparent"
                  >
                    <X className="h-2 w-2" />
                  </Button>
                </Badge>
              )}
              {activeFilterCount > 0 && (
                <Button
                  variant="ghost"
                  size="sm"
                  onClick={onReset}
                  className="text-xs h-6 px-2"
                >
                  Xóa tất cả ({activeFilterCount})
                </Button>
              )}
            </>
          )}
        </div>

        {/* Results Count */}
        <div className="text-sm text-gray-500">
          {hasActiveFilters ? (
            <>
              Hiển thị {filteredCount} / {totalCount} categories
            </>
          ) : (
            <>
              Tổng cộng {totalCount} categories
            </>
          )}
        </div>
      </div>
    </div>
  );
}
