import { IssueType } from "@/shared/types/issueTypes";
import { useIssueType } from "../useIssueTypeQuery";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  createColumnHelper,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { IssueTypeDialog } from "./IssueTypeDialog";
import { EditIcon } from "lucide-react";
import { DndContext } from "@dnd-kit/core";
import { SortableContext } from "@dnd-kit/sortable";
import { DraggableRow } from "./IssueTypeDraggableRow";
import { restrictToVerticalAxis } from "@dnd-kit/modifiers";

const columnHelper = createColumnHelper<IssueType>();

const columns = [
  columnHelper.accessor("name", {
    header: "Tên",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <div
          className="h-4 w-4 rounded-full"
          style={{ backgroundColor: row.original.color }}
        />
        {row.getValue("name")}
      </div>
    ),
  }),
  columnHelper.accessor("color", {
    header: "Màu sắc",
  }),
  columnHelper.accessor("description", {
    header: "Mô tả",
  }),
  columnHelper.accessor("active", {
    header: "Hoạt động",
    cell: ({ row }) => (
      <div className="capitalize">
        {row.getValue("active") ? "Có" : "Không"}
      </div>
    ),
  }),
  columnHelper.display({
    id: "actions",
    header: "Hành động",
    cell: ({ row }) => {
      return (
        <div className="flex gap-2">
          <IssueTypeDialog
            mode="edit"
            issueType={row.original}
            trigger={
              <Button>
                <EditIcon />
              </Button>
            }
          ></IssueTypeDialog>
          <Button
            variant="secondary"
            onClick={() => console.log("Delete", row.original.id)}
            className="text-red-600 hover:underline"
          >
            Xóa
          </Button>
        </div>
      );
    },
  }),
];

export function IssueTypeList() {
  const { data: issueType } = useIssueType();
  const table = useReactTable({
    data: issueType || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  function handleDragEnd() {}

  return (
    <div>
      {issueType?.length === 0 && <p>Chưa có issue type nào</p>}
      {issueType && (
        <Table className="mt-4 w-full">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            <DndContext onDragEnd={handleDragEnd}>
              <DndContext
                onDragEnd={handleDragEnd}
                modifiers={[restrictToVerticalAxis]}
              >
                <SortableContext
                  items={table.getRowModel().rows.map((r) => r.id)}
                >
                  {table.getRowModel().rows.map((row) => (
                    <DraggableRow key={row.id} row={row} />
                  ))}
                </SortableContext>
              </DndContext>
            </DndContext>
          </TableBody>
        </Table>
      )}
      <div className="mt-4">
        <div>Kéo thả để sắp xếp thứ tự</div>
      </div>
    </div>
  );
}
