import { IssueType } from "@/shared/types/issueTypes";
import { useIssueType } from "../useIssueTypeQuery";
import {
  useReactTable,
  getCoreRowModel,
  flexRender,
  createColumnHelper,
} from "@tanstack/react-table";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { IssueTypeDialog } from "./IssueTypeDialog";
import { EditIcon } from "lucide-react";

const columnHelper = createColumnHelper<IssueType>();

const columns = [
  columnHelper.accessor("name", {
    header: "Tên",
    cell: ({ row }) => (
      <div className="flex items-center gap-2">
        <div
          className="h-4 w-4 rounded-full"
          style={{ backgroundColor: row.original.color }}
        />
        {row.getValue("name")}
      </div>
    ),
  }),
  columnHelper.accessor("color", {
    header: "<PERSON><PERSON>u sắc",
  }),
  columnHelper.accessor("description", {
    header: "<PERSON><PERSON> tả",
  }),
  columnHelper.accessor("active", {
    header: "Hoạt động",
  }),
  columnHelper.display({
    id: "actions",
    header: "Hành động",
    cell: ({ row }) => {
      return (
        <div className="flex gap-2">
          <IssueTypeDialog
            mode="edit"
            issueType={row.original}
            trigger={
              <Button>
                <EditIcon />
              </Button>
            }
          ></IssueTypeDialog>
          <Button
            variant="secondary"
            onClick={() => console.log("Delete", row.original.id)}
            className="text-red-600 hover:underline"
          >
            Xóa
          </Button>
        </div>
      );
    },
  }),
];

export function IssueTypeList() {
  const { data: issueType } = useIssueType();
  const table = useReactTable({
    data: issueType || [],
    columns,
    getCoreRowModel: getCoreRowModel(),
  });

  return (
    <div>
      {issueType?.length === 0 && <p>Chưa có issue type nào</p>}
      {issueType && (
        <Table className="mt-4 w-full">
          <TableHeader>
            {table.getHeaderGroups().map((headerGroup) => (
              <TableRow key={headerGroup.id}>
                {headerGroup.headers.map((header) => (
                  <TableHead key={header.id}>
                    {flexRender(
                      header.column.columnDef.header,
                      header.getContext()
                    )}
                  </TableHead>
                ))}
              </TableRow>
            ))}
          </TableHeader>
          <TableBody>
            {table.getRowModel().rows.map((row) => (
              <TableRow key={row.id}>
                {row.getVisibleCells().map((cell) => (
                  <TableCell key={cell.id}>
                    {flexRender(cell.column.columnDef.cell, cell.getContext())}
                  </TableCell>
                ))}
              </TableRow>
            ))}
          </TableBody>
        </Table>
      )}
    </div>
  );
}
