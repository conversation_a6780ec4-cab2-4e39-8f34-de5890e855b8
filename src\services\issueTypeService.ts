import { IssueType } from "@/shared/types/issueTypes";

const mockIssueTypes: IssueType[] = [
  {
    id: "1",
    name: "<PERSON><PERSON>",
    color: "#EF4444",
    description: "Lỗi hệ thống, cần x<PERSON> lý gấp",
    active: true,
  },
  {
    id: "2",
    name: "Feature",
    color: "#3B82F6",
    description: "<PERSON><PERSON><PERSON> cầu tính năng mới",
    active: true,
  },
  {
    id: "3",
    name: "Enhancement",
    color: "#10B981",
    description: "Cải tiến tính năng hiện có",
    active: true,
  },
  {
    id: "4",
    name: "Task",
    color: "#6366F1",
    description: "<PERSON><PERSON><PERSON> việc thường xuyên",
    active: true,
  },
  {
    id: "5",
    name: "Documentation",
    color: "#F59E0B",
    description: "Cập nhật tài liệu dự án",
    active: false,
  },
  {
    id: "6",
    name: "Research",
    color: "#14B8A6",
    description: "Khảo sát giải pháp hoặc xu hướng",
    active: true,
  },
  {
    id: "7",
    name: "UI Fix",
    color: "#E879F9",
    description: "Sửa lỗi giao diện người dùng",
    active: true,
  },
  {
    id: "8",
    name: "Backend Issue",
    color: "#F87171",
    description: "Lỗi phía máy chủ hoặc API",
    active: true,
  },
];
export const issueTypeService = {
  getAll: async () => mockIssueTypes,

  //create issueType
  createIssueType: async (issueType: IssueType) => {
    mockIssueTypes.push(issueType);
    return issueType;
  },
};
