import { IssueType } from "@/shared/types/issueTypes";

const mockIssueTypes: IssueType[] = [
  {
    id: "1",
    name: "<PERSON><PERSON>",
    color: "#EF4444",
    description: "Lỗi hệ thống, cần xử lý gấp",
    active: true,
  },
  {
    id: "2",
    name: "Feature",
    color: "#3B82F6",
    description: "<PERSON><PERSON><PERSON> cầu tính năng mới",
    active: true,
  },
  {
    id: "3",
    name: "Enhancement",
    color: "#10B981",
    description: "<PERSON><PERSON>i tiến tính năng hiện có",
    active: true,
  },
  {
    id: "4",
    name: "Task",
    color: "#6366F1",
    description: "<PERSON><PERSON><PERSON> việc thường xuyên",
    active: true,
  },
  {
    id: "5",
    name: "Documentation",
    color: "#F59E0B",
    description: "C<PERSON>p nhật tài liệu dự án",
    active: false,
  },
  {
    id: "6",
    name: "<PERSON>",
    color: "#14B8A6",
    description: "Khảo sát giải pháp hoặc xu hướng",
    active: true,
  },
  {
    id: "7",
    name: "UI Fix",
    color: "#E879F9",
    description: "Sửa lỗi giao diện người dùng",
    active: true,
  },
  {
    id: "8",
    name: "Backend Issue",
    color: "#F87171",
    description: "Lỗi phía máy chủ hoặc API",
    active: true,
  },
  {
    id: "9",
    name: "DevOps",
    color: "#7C3AED",
    description: "Vấn đề liên quan đến hạ tầng CI/CD",
    active: false,
  },
  {
    id: "10",
    name: "Design",
    color: "#FBBF24",
    description: "Yêu cầu thiết kế UI/UX mới",
    active: true,
  },
  {
    id: "11",
    name: "Performance",
    color: "#0EA5E9",
    description: "Tối ưu hóa hiệu năng hệ thống",
    active: true,
  },
  {
    id: "12",
    name: "Security",
    color: "#DC2626",
    description: "Vấn đề bảo mật cần điều tra",
    active: true,
  },
  {
    id: "13",
    name: "Refactor",
    color: "#22C55E",
    description: "Tái cấu trúc lại mã nguồn",
    active: false,
  },
  {
    id: "14",
    name: "Accessibility",
    color: "#A78BFA",
    description: "Cải thiện khả năng truy cập",
    active: true,
  },
  {
    id: "15",
    name: "Testing",
    color: "#D946EF",
    description: "Viết hoặc sửa test case",
    active: true,
  },
  {
    id: "16",
    name: "Translation",
    color: "#38BDF8",
    description: "Thêm hoặc chỉnh sửa bản dịch",
    active: false,
  },
  {
    id: "17",
    name: "Integration",
    color: "#4ADE80",
    description: "Tích hợp hệ thống hoặc dịch vụ",
    active: true,
  },
  {
    id: "18",
    name: "Mobile",
    color: "#FACC15",
    description: "Vấn đề liên quan đến app di động",
    active: true,
  },
  {
    id: "19",
    name: "Analytics",
    color: "#60A5FA",
    description: "Yêu cầu hoặc lỗi phân tích dữ liệu",
    active: true,
  },
  {
    id: "20",
    name: "Other",
    color: "#94A3B8",
    description: "Không nằm trong các loại đã định nghĩa",
    active: true,
  },
];
export const issueTypeService = {
  getAll: async () => mockIssueTypes,


  //create issueType
  createIssueType: async (issueType: IssueType) => {
    mockIssueTypes.push(issueType);
    return issueType;
  }
};
