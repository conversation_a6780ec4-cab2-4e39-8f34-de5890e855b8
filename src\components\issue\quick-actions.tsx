"use client"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { cn } from "@/lib/utils"
import { AtSign, Calendar, ChevronDown, ChevronRight, Eye, EyeOff, Link, List, MessageSquare } from "lucide-react"

// Mock data for comments/activity
const activity = [
  {
    operationId: "1",
    userId: "user1",
    userName: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    changeDate: "2025-07-20T14:30:00Z",
    comment:
      "<p>I've investigated this issue and found that the login system is failing because of an expired SSL certificate.</p>",
    activities: [
      {
        field: "Status",
        oldValue: "Open",
        newValue: "In Progress",
      },
    ],
    taggedUsers: [
      {
        id: "user2",
        userId: "user2",
        name: "<PERSON>",
        avatar: "/placeholder.svg?height=28&width=28",
        isRead: true,
      },
      {
        id: "user3",
        userId: "user3",
        name: "Mike Johnson",
        avatar: "/placeholder.svg?height=28&width=28",
        isRead: false,
      },
    ],
  },
  {
    operationId: "2",
    userId: "user2",
    userName: "Jane Smith",
    avatar: "/placeholder.svg?height=40&width=40",
    changeDate: "2025-07-21T09:15:00Z",
    comment: "<p>I've assigned this to our security team. They'll need to renew the certificate ASAP.</p>",
    activities: [
      {
        field: "Assignee",
        oldValue: "John Doe",
        newValue: "Security Team",
      },
      {
        field: "Priority",
        oldValue: "Medium",
        newValue: "High",
      },
    ],
    taggedUsers: [],
  },
]

// Mock data for dropdowns
const listStatus = [
  { id: 1, name: "Open" },
  { id: 2, name: "In Progress" },
  { id: 3, name: "Resolved" },
  { id: 4, name: "Closed" },
]

const listUser = [
  { id: 1, name: "John Doe" },
  { id: 2, name: "Jane Smith" },
  { id: 3, name: "Mike Johnson" },
  { id: 4, name: "Sarah Williams" },
]

const listVersions = [
  { label: "v1.0.0", value: "1" },
  { label: "v1.1.0", value: "2" },
  { label: "v1.2.0", value: "3" },
  { label: "v1.2.3", value: "4" },
]

export default function QuickActions() {
  const [expandedComment, setExpandedComment] = useState(true)
  const [expanded, setExpanded] = useState(false)
  const [draftComment, setDraftComment] = useState("")
  const [previewShow, setPreviewShow] = useState(false)


  const handleExpand = () => {
    setExpanded(true)
    setPreviewShow(false)
  }

  const handleClose = () => {
    setExpanded(false)
    setPreviewShow(false)
  }

  const handleReview = () => {
    setPreviewShow(true)
    setExpanded(false)
  }

  const handleEdit = () => {
    setPreviewShow(false)
    setExpanded(true)
  }

  const handleSubmit = () => {
    // Handle submission logic
    setDraftComment("")
    setPreviewShow(false)
    setExpanded(false)
  }

  return (
    <>
      {/* Comments Section */}
      <div className="container mx-auto max-w-6xl mb-24">
        <Card className="bg-white shadow-none border-none rounded-xl">
          <CardHeader className="bg-gradient-to-r from-blue-50 to-indigo-50 py-4">
            <CardTitle className="flex items-center justify-between text-lg font-medium">
              <div className="flex items-center">
                <span className="text-gray-800">Comments</span>
                <Badge className="ml-2 bg-blue-100 text-blue-800 text-xs">{activity.length}</Badge>
              </div>
              <Button
                variant="ghost"
                size="sm"
                className="hover:bg-blue-100 rounded-full p-2"
                onClick={() => setExpandedComment(!expandedComment)}
              >
                {expandedComment ? (
                  <ChevronDown className="h-5 w-5 text-gray-600" />
                ) : (
                  <ChevronRight className="h-5 w-5 text-gray-600" />
                )}
              </Button>
            </CardTitle>
          </CardHeader>

          {expandedComment && (
            <CardContent className="p-4">
              <div className="space-y-4">
                {activity.map((item) => (
                  <div
                    key={item.operationId}
                    className="bg-gray-50/50 rounded-lg p-4 hover:bg-gray-50/80 transition-all border border-gray-100 relative"
                  >
                    <div className="flex gap-3">
                      <Avatar className="h-10 w-10 ring-2 ring-white shadow-sm">
                        <AvatarImage
                          src={item?.avatar || "/placeholder.svg?height=40&width=40"}
                          alt={item.userName || "User"}
                        />
                        <AvatarFallback className="bg-blue-600 text-white font-medium">
                          {item.userName?.[0] || "U"}
                        </AvatarFallback>
                      </Avatar>
                      <div className="flex-1 space-y-3">
                        <div className="flex items-center gap-2 text-sm">
                          <a
                            href={`/profile/${item.userId}`}
                            className="font-medium text-gray-900 hover:text-blue-600 transition-colors"
                          >
                            {item.userName || "Unknown User"}
                          </a>
                          <Badge variant="secondary" className="text-xs px-2 py-0.5">
                            {item?.changeDate ? new Date(item.changeDate).toLocaleDateString("vi-VN") : "No date"}
                          </Badge>
                        </div>

                        {item.comment && item.comment.length > 0 && (
                          <div className="bg-blue-50 border-l-4 border-blue-400 p-3 rounded-r">
                            <div className="prose max-w-none" dangerouslySetInnerHTML={{ __html: item.comment }} />
                          </div>
                        )}

                        {item.activities && item.activities.length > 0 && (
                          <div className="bg-white border rounded-lg p-3 space-y-2 shadow-sm">
                            <h4 className="text-xs font-semibold text-gray-500 uppercase tracking-wide mb-2">
                              Changes Made
                            </h4>
                            {item.activities.map((change, idx) => {
                              const oldValue = change.oldValue || ""
                              const newValue = change.newValue || ""
                              return (
                                <div key={idx} className="flex items-center gap-2 text-sm">
                                  <Badge variant="outline" className="text-xs font-medium min-w-fit">
                                    {change.field || "Unknown Field"}
                                  </Badge>
                                  <div className="flex items-center gap-2 text-gray-600">
                                    {oldValue && (
                                      <span className="bg-red-50 text-red-700 px-2 py-1 rounded text-xs">
                                        {oldValue || "empty"}
                                      </span>
                                    )}
                                    <span className="text-blue-500 font-medium">→</span>
                                    <span className="bg-green-50 text-green-700 px-2 py-1 rounded text-xs font-medium">
                                      {newValue || "empty"}
                                    </span>
                                  </div>
                                </div>
                              )
                            })}
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Tagged Users Section */}
                    {item.taggedUsers && item.taggedUsers.length > 0 && (
                      <div className="absolute bottom-3 right-3">
                        <div className="flex items-center gap-1">
                          <span className="text-xs text-gray-500 mr-2">Tagged:</span>
                          <div className="flex space-x-1">
                            {item.taggedUsers.slice(0, 5).map((user) => (
                              <div key={user.id} className="relative group">
                                <a href={`/profile/${user.userId}`}>
                                  <div className="relative">
                                    <Avatar
                                      className={`h-8 w-8 border-2 hover:scale-110 transition-transform cursor-pointer ${
                                        user.isRead ? "border-green-400 ring-1 ring-green-200" : ""
                                      }`}
                                    >
                                      <AvatarImage
                                        src={user.avatar || "/placeholder.svg?height=28&width=28" || "/placeholder.svg"}
                                        alt={user.name}
                                      />
                                      <AvatarFallback className="text-xs bg-gray-100">
                                        {user.name[0] || "U"}
                                      </AvatarFallback>
                                    </Avatar>
                                    {/* Read/Unread Indicator with Icon */}
                                    <div
                                      className={`absolute -top-0.5 -right-0.5 w-4 h-4 rounded-full border-1 border-white shadow-sm flex items-center justify-center ${
                                        user.isRead ? "bg-green-500" : "bg-black"
                                      }`}
                                    >
                                      {user.isRead ? (
                                        <Eye className="w-3 h-3 text-white" />
                                      ) : (
                                        <EyeOff className="w-2.5 h-2.5 text-white" />
                                      )}
                                    </div>
                                  </div>
                                </a>
                                <div className="absolute -top-10 left-1/2 transform -translate-x-1/2 bg-black text-white text-xs rounded px-2 py-1 opacity-0 group-hover:opacity-100 transition-opacity whitespace-nowrap">
                                  {user.name} • {user.isRead ? "Viewed" : "Unviewed"}
                                </div>
                              </div>
                            ))}
                            {/* Show count if more than 5 users */}
                            {item.taggedUsers.length > 5 && (
                              <div className="flex items-center justify-center h-7 w-7 bg-gray-200 border-2 border-white rounded-full text-xs font-medium text-gray-600">
                                +{item.taggedUsers.length - 5}
                              </div>
                            )}
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                ))}
              </div>
            </CardContent>
          )}
        </Card>
      </div>

      {/* Fixed Bottom Bar */}
      <div
        className={cn(
          "fixed bottom-0 left-0 right-0 z-1 bg-white/80 backdrop-blur-sm border-t",
          expanded ? "p-4" : "p-3",
        )}
      >
        {!expanded ? (
          !previewShow ? (
            <div className="max-w-2xl mx-auto flex gap-2">
              <div
                onClick={handleExpand}
                className="flex-1 px-3 py-2 bg-gray-50 rounded text-sm cursor-text hover:bg-gray-100"
              >
                Write a comment...
              </div>
              <Button variant="outline" onClick={handleExpand} size="sm">
                <MessageSquare className="h-4 w-4 mr-2" />
                Comment
              </Button>
            </div>
          ) : (
            <div className="flex justify-center gap-2">
              <Button variant="ghost" onClick={handleEdit} size="sm">
                Edit
              </Button>
              <Button onClick={handleSubmit} size="sm" className="bg-blue-600 text-white hover:bg-blue-700">
                Submit
              </Button>
            </div>
          )
        ) : (
          <div className="max-w-4xl mx-auto grid md:grid-cols-3 gap-4">
            <div className="md:col-span-2">
              {/* Simplified editor for demo purposes */}
              <div className="border rounded-lg bg-white">
                <div className="p-2 border-b flex items-center gap-1">
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 font-bold">
                    B
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 italic">
                    I
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0 underline">
                    U
                  </Button>
                  <Separator orientation="vertical" className="h-6 mx-2" />
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <List className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <Link className="h-4 w-4" />
                  </Button>
                  <Button variant="ghost" size="sm" className="h-8 w-8 p-0">
                    <AtSign className="h-4 w-4" />
                  </Button>
                </div>
                <textarea
                  className="w-full p-3 min-h-[300px] focus:outline-none resize-none"
                  placeholder="Write your comment here..."
                  value={draftComment}
                  onChange={(e) => setDraftComment(e.target.value)}
                />
              </div>
              <div className="flex justify-end gap-2 mt-2">
                <Button variant="ghost" onClick={handleClose} size="sm">
                  Cancel
                </Button>
                <Button variant="outline" onClick={handleReview} size="sm">
                  Review
                </Button>
                <Button onClick={handleSubmit} size="sm" className="bg-blue-600 text-white hover:bg-blue-700">
                  Submit
                </Button>
              </div>
            </div>
            <div className="space-y-3 bg-gray-50/50 p-3 rounded-lg">
              <div>
                <label className="text-xs font-medium mb-1 block">Status</label>
                <select className="w-full text-sm border rounded p-2">
                  {listStatus.map((status) => (
                    <option key={status.id} value={status.id}>
                      {status.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="text-xs font-medium mb-1 block">Assignee</label>
                <select className="w-full text-sm border rounded p-2">
                  <option value="">Select assignee</option>
                  {listUser.map((user) => (
                    <option key={user.id} value={user.id}>
                      {user.name}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="text-xs font-medium mb-1 block">Versions</label>
                <select className="w-full text-sm border rounded p-2">
                  <option value="">Select version</option>
                  {listVersions.map((version) => (
                    <option key={version.value} value={version.value}>
                      {version.label}
                    </option>
                  ))}
                </select>
              </div>
              <div>
                <label className="text-xs font-medium mb-1 block">Due Date</label>
                <div className="flex items-center gap-1 bg-white border rounded p-2 text-sm">
                  <Calendar className="h-4 w-4 text-gray-400" />
                  <span>Select date</span>
                </div>
              </div>
              <div>
                <label className="text-xs font-medium mb-1 block">Notify Users</label>
                <div className="flex items-center gap-1 bg-white border rounded p-2 text-sm">
                  <AtSign className="h-4 w-4 text-gray-400" />
                  <span>Select users to notify</span>
                </div>
              </div>
            </div>
          </div>
        )}
      </div>
    </>
  )
}
