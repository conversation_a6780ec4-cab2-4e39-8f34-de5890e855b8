"use client"
import { IssueTypeManager } from "@/features/settings/issueType/components/IssueTypeManager";
import { useIssueType } from "@/features/settings/issueType/useIssueTypeQuery";

export default function IssueType(){
    const {data:issueType} = useIssueType();
    console.log(issueType);
    return(
        <div>
            <h1>Issue Type</h1>
            {issueType?.map((item) => (
                <div key={item.id}>{item.name}</div>
            ))}
            <IssueTypeManager />
        </div>
    );
}