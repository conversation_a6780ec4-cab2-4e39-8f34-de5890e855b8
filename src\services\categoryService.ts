import { CategoryFormValues } from "./../schema/categorySchema";
import { Category } from "@/shared/types/categoryTypes";

let mockDB: Category[] = [];

export const categoryService = {
  create: async (input: CategoryFormValues): Promise<Category> => {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    const newCategory: Category = {
      id: crypto.randomUUID(),
      name: input.name,
      description: input.description,
    };
    mockDB.push(newCategory);
    return newCategory;
  },

  getAll: async (): Promise<Category[]> => {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    return mockDB;
  },

  delete: async (id: string): Promise<void> => {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    mockDB = mockDB.filter((category) => category.id !== id);
  },

  update: async (id: string, input: CategoryFormValues): Promise<void> => {
    await new Promise((resolve) => setTimeout(resolve, 1000));
    const category = mockDB.find((category) => category.id === id);
    if (category) {
      category.name = input.name;
      category.description = input.description;
    }
  },
};
