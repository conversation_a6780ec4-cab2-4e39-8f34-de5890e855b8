"use client";

import { useState } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { But<PERSON> } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Label } from "@/components/ui/label";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { Badge } from "@/components/ui/badge";
import { Loader2, Palette, Save, X } from "lucide-react";
import {
  createCategorySchema,
  updateCategorySchema,
  CreateCategoryFormValues,
  UpdateCategoryFormValues,
  VALID_CATEGORY_COLORS,
} from "@/schema/categorySchema";
import { Category } from "@/shared/types/categoryTypes";

interface CategoryFormProps {
  category?: Category; // Nếu có thì là edit mode
  onSubmit: (data: CreateCategoryFormValues | UpdateCategoryFormValues) => Promise<void> | void;
  onCancel: () => void;
  isLoading?: boolean;
  mode?: "create" | "edit";
}

export default function CategoryForm({
  category,
  onSubmit,
  onCancel,
  isLoading = false,
  mode = category ? "edit" : "create",
}: CategoryFormProps) {
  const [selectedColor, setSelectedColor] = useState(
    category?.color || VALID_CATEGORY_COLORS[0]
  );

  const schema = mode === "edit" ? updateCategorySchema : createCategorySchema;
  
  const {
    register,
    handleSubmit,
    formState: { errors, isDirty },
    setValue,
    watch,
  } = useForm<CreateCategoryFormValues | UpdateCategoryFormValues>({
    resolver: zodResolver(schema),
    defaultValues: {
      name: category?.name || "",
      description: category?.description || "",
      color: category?.color || VALID_CATEGORY_COLORS[0],
      isActive: category?.isActive ?? true,
      projectId: category?.projectId || "",
      tags: category?.tags || [],
      ...(mode === "edit" && category ? { id: category.id } : {}),
    },
  });

  const watchedName = watch("name");

  const handleColorSelect = (color: string) => {
    setSelectedColor(color);
    setValue("color", color, { shouldDirty: true });
  };

  const onFormSubmit = (data: CreateCategoryFormValues | UpdateCategoryFormValues) => {
    onSubmit({
      ...data,
      color: selectedColor,
    });
  };

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <Palette className="h-5 w-5" />
          {mode === "edit" ? "Chỉnh sửa Category" : "Tạo Category mới"}
        </CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit(onFormSubmit)} className="space-y-6">
          {/* Preview */}
          {watchedName && (
            <div className="p-4 bg-gray-50 rounded-lg">
              <Label className="text-sm font-medium text-gray-700 mb-2 block">
                Preview
              </Label>
              <Badge
                style={{ backgroundColor: selectedColor }}
                className="text-white"
              >
                {watchedName}
              </Badge>
            </div>
          )}

          {/* Tên Category */}
          <div className="space-y-2">
            <Label htmlFor="name">
              Tên Category <span className="text-red-500">*</span>
            </Label>
            <Input
              id="name"
              {...register("name")}
              placeholder="Nhập tên category..."
              className={errors.name ? "border-red-500" : ""}
            />
            {errors.name && (
              <p className="text-sm text-red-500">{errors.name.message}</p>
            )}
          </div>

          {/* Mô tả */}
          <div className="space-y-2">
            <Label htmlFor="description">Mô tả</Label>
            <Textarea
              id="description"
              {...register("description")}
              placeholder="Nhập mô tả cho category..."
              rows={3}
              className={errors.description ? "border-red-500" : ""}
            />
            {errors.description && (
              <p className="text-sm text-red-500">{errors.description.message}</p>
            )}
          </div>

          {/* Màu sắc */}
          <div className="space-y-3">
            <Label>Màu sắc</Label>
            <div className="grid grid-cols-5 gap-3">
              {VALID_CATEGORY_COLORS.map((color) => (
                <button
                  key={color}
                  type="button"
                  onClick={() => handleColorSelect(color)}
                  className={`w-12 h-12 rounded-lg border-2 transition-all hover:scale-110 ${
                    selectedColor === color
                      ? "border-gray-900 ring-2 ring-gray-300"
                      : "border-gray-200 hover:border-gray-400"
                  }`}
                  style={{ backgroundColor: color }}
                  title={color}
                />
              ))}
            </div>
            <div className="flex items-center gap-2">
              <Label htmlFor="customColor" className="text-sm">
                Hoặc chọn màu tùy chỉnh:
              </Label>
              <input
                type="color"
                id="customColor"
                value={selectedColor}
                onChange={(e) => handleColorSelect(e.target.value)}
                className="w-8 h-8 rounded border border-gray-300 cursor-pointer"
              />
            </div>
          </div>

          {/* Trạng thái */}
          <div className="flex items-center space-x-2">
            <input
              type="checkbox"
              id="isActive"
              {...register("isActive")}
              className="rounded border-gray-300 text-blue-600 focus:ring-blue-500"
            />
            <Label htmlFor="isActive" className="text-sm font-medium">
              Kích hoạt category
            </Label>
          </div>

          {/* Actions */}
          <div className="flex justify-end gap-3 pt-4 border-t">
            <Button
              type="button"
              variant="outline"
              onClick={onCancel}
              disabled={isLoading}
            >
              <X className="w-4 h-4 mr-2" />
              Hủy
            </Button>
            <Button
              type="submit"
              disabled={isLoading || !isDirty}
              className="bg-blue-600 hover:bg-blue-700"
            >
              {isLoading ? (
                <Loader2 className="w-4 h-4 mr-2 animate-spin" />
              ) : (
                <Save className="w-4 h-4 mr-2" />
              )}
              {mode === "edit" ? "Cập nhật" : "Tạo mới"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
