import { z } from "zod";

// Schema cho việc tạo mới category
export const createCategorySchema = z.object({
  name: z
    .string()
    .min(1, "Tên category không được để trống")
    .max(50, "Tên category không được quá 50 ký tự")
    .regex(/^[a-zA-Z0-9\s\u00C0-\u024F\u1E00-\u1EFF]+$/, "Tên category chỉ được chứa chữ cái, số và khoảng trắng"),
  description: z
    .string()
    .max(200, "<PERSON><PERSON> tả không được quá 200 ký tự")
    .optional(),
  color: z
    .string()
    .regex(/^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$/, "Màu sắc phải là mã hex hợp lệ")
    .default("#6B7280"),
  isActive: z.boolean().default(true),
});

// Schema cho việc cập nhật category
export const updateCategorySchema = createCategorySchema.partial().extend({
  id: z.string().min(1, "ID category không hợp lệ"),
});

// Schema cho việc sắp xếp lại category
export const reorderCategorySchema = z.object({
  categories: z.array(z.object({
    id: z.string(),
    order: z.number(),
  })),
});

// Schema cho việc xóa category
export const deleteCategorySchema = z.object({
  id: z.string().min(1, "ID category không hợp lệ"),
});

// Export types
export type CreateCategoryFormValues = z.infer<typeof createCategorySchema>;
export type UpdateCategoryFormValues = z.infer<typeof updateCategorySchema>;
export type ReorderCategoryValues = z.infer<typeof reorderCategorySchema>;
export type DeleteCategoryValues = z.infer<typeof deleteCategorySchema>;

// Backward compatibility
export const categorySchema = createCategorySchema;
export type CategoryFormValues = CreateCategoryFormValues;
