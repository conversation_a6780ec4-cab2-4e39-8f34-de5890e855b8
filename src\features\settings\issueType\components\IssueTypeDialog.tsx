import { But<PERSON> } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  DialogContent,
  <PERSON><PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";
import { IssueTypeForm } from "./IssueTypeForm";
import { IssueType } from "@/shared/types/issueTypes";

interface IssueTypeDialogProps {
  mode: "create" | "edit";
  issueType?: IssueType;
  trigger?: React.ReactNode;
}



export function IssueTypeDialog({
  mode,
  issueType,
}: IssueTypeDialogProps) {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
  };
  const handleCancel = () => {
    setOpen(false);
  };
  return (
    <>
      <Button onClick={() => setOpen(true)}>Tạo Issue Type</Button>
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {mode === "create"
                ? "Tạo Issue Type mới"
                : `Sửa "${issueType?.name}"`}
            </DialogTitle>
          </DialogHeader>

          <IssueTypeForm
            issueType={issueType}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
