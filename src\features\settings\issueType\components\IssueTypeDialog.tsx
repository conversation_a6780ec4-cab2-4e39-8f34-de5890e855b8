import { Button } from "@/components/ui/button";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ontent,
  Di<PERSON>Header,
  DialogTitle,
} from "@/components/ui/dialog";
import { useState } from "react";
import { IssueTypeForm } from "./IssueTypeForm";
import { IssueType } from "@/shared/types/issueTypes";

interface IssueTypeDialogProps {
  mode: "create" | "edit";
  issueType?: IssueType;
  trigger?: React.ReactNode;
}



export function IssueTypeDialog({
  mode,
  issueType,
  trigger,
}: IssueTypeDialogProps) {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
  };
  const handleCancel = () => {
    setOpen(false);
  };
  return (
    <>
      {trigger ? (
        <div onClick={() => setOpen(true)}>{trigger}</div>
      ) : (
        <Button onClick={() => setOpen(true)}>
          {mode === "create" ? "Tạo Issue Type" : "Sửa Issue Type"}
        </Button>
      )}
      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>
              {mode === "create"
                ? "Tạo Issue Type mới"
                : `Sửa "${issueType?.name}"`}
            </DialogTitle>
          </DialogHeader>

          <IssueTypeForm
            issueType={issueType}
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
