import { useQueryClient } from "./../../../../node_modules/@tanstack/react-query/src/QueryClientProvider";
import { categoryService } from "@/services/categoryService";
export const useCreateCategory = () => {
  const queryClient = useQueryClient();
  return {
    mutationFn: categoryService.create,
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["categories"] });
    },
    onError: () => {},
  };
};
