import { issueTypeService } from "@/services/issueTypeService"
import { useMutation, useQuery, useQueryClient } from "@tanstack/react-query"
import { toast } from "sonner"

export const useIssueType = () =>{
  return useQuery({
    queryKey: ["issueTypes"],
    queryFn: issueTypeService.getAll,
  })
}

export const useCreateIssueType = () => {
  const queryClient = useQueryClient();
  return useMutation ({
    mutationFn: issueTypeService.createIssueType,
    onSuccess: (issueType) => {
       queryClient.invalidateQueries({ queryKey: ["issueTypes"] });
       toast.success(`Issue Type "${issueType.name}" đã được tạo thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi khi tạo Issue Type: ${error.message}`);
    },
  })

}