# 🚀 Project Management System

Hệ thống quản lý dự án được xây dựng với Next.js, TypeScript, và TailwindCSS.

## 📋 Tech Stack

- **Framework**: Next.js 15.3.4 với App Router
- **Language**: TypeScript
- **Styling**: TailwindCSS v4 + Radix UI
- **State Management**: TanStack React Query
- **Forms**: React Hook Form + Zod validation
- **UI Components**: Radix UI primitives + shadcn/ui
- **Icons**: Lucide React

## 🏃‍♂️ Getting Started

```bash
# Cài đặt dependencies
npm install

# Chạy development server
npm run dev

# Mở http://localhost:3001
```

---

# 📖 HƯỚNG DẪN TẠO CHỨC NĂNG MỚI

## 🎯 Quy trình 5 bước chuẩn

### **Bước 1: Định nghĩa Types (5 phút)**
```typescript
// src/shared/types/[feature]Types.ts
export type MyFeature = {
  id: string;
  name: string;
  description?: string;
  createdAt: string;
  updatedAt: string;
  isActive: boolean;
};

export type CreateMyFeatureRequest = {
  name: string;
  description?: string;
  isActive?: boolean;
};

export type UpdateMyFeatureRequest = Partial<CreateMyFeatureRequest> & {
  id: string;
};
```

### **Bước 2: Tạo Service với Mock Data (10 phút)**
```typescript
// src/services/[feature]Service.ts
const mockData: MyFeature[] = [
  {
    id: "1",
    name: "Sample Item",
    description: "Mô tả mẫu",
    createdAt: new Date().toISOString(),
    updatedAt: new Date().toISOString(),
    isActive: true,
  },
];

export type CreateMyFeatureRequest = {
  name: string;
  description?: string;
  isActive?: boolean;
};

export const myFeatureService = {
  getAll: async (): Promise<MyFeature[]> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    return [...mockData];
  },

  create: async (data: CreateMyFeatureRequest): Promise<MyFeature> => {
    await new Promise(resolve => setTimeout(resolve, 800));

    const newItem: MyFeature = {
      id: crypto.randomUUID(),
      name: data.name,
      description: data.description,
      isActive: data.isActive ?? true,
      createdAt: new Date().toISOString(),
      updatedAt: new Date().toISOString(),
    };

    mockData.push(newItem);
    return newItem;
  },

  delete: async (id: string): Promise<void> => {
    await new Promise(resolve => setTimeout(resolve, 500));
    const index = mockData.findIndex(item => item.id === id);
    if (index > -1) {
      mockData.splice(index, 1);
    }
  },
};
```

### **Bước 3: Tạo Hooks (10 phút)**
```typescript
// src/features/[feature]/use[Feature]Query.ts
import { useQuery, useMutation, useQueryClient } from "@tanstack/react-query";
import { myFeatureService, CreateMyFeatureRequest } from "@/services/myFeatureService";
import { toast } from "sonner";

export const useMyFeatures = () => {
  return useQuery({
    queryKey: ["myFeatures"],
    queryFn: myFeatureService.getAll,
    staleTime: 5 * 60 * 1000,
  });
};

export const useCreateMyFeature = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (data: CreateMyFeatureRequest) => myFeatureService.create(data),
    onSuccess: (newItem) => {
      queryClient.refetchQueries({ queryKey: ["myFeatures"] });
      toast.success(`${newItem.name} đã được tạo thành công!`);
    },
    onError: (error: Error) => {
      toast.error(`Lỗi: ${error.message}`);
    },
  });
};

export const useDeleteMyFeature = () => {
  const queryClient = useQueryClient();
  return useMutation({
    mutationFn: (id: string) => myFeatureService.delete(id),
    onSuccess: () => {
      queryClient.refetchQueries({ queryKey: ["myFeatures"] });
      toast.success("Đã xóa thành công!");
    },
    onError: (error: Error) => {
      toast.error(`Lỗi: ${error.message}`);
    },
  });
};
```

### **Bước 4: Tạo Components (20 phút)**

#### **4.1 Form Component**
```typescript
// src/features/[feature]/components/MyFeatureForm.tsx
"use client";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useCreateMyFeature } from "../useMyFeatureQuery";

interface MyFeatureFormProps {
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function MyFeatureForm({ onSuccess, onCancel }: MyFeatureFormProps) {
  const [name, setName] = useState("");
  const [description, setDescription] = useState("");
  const createMutation = useCreateMyFeature();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    try {
      await createMutation.mutateAsync({
        name: name.trim(),
        description: description.trim() || undefined,
      });

      // Reset form
      setName("");
      setDescription("");

      // Callback
      onSuccess?.();

    } catch (error) {
      // Error handled in mutation
    }
  };

  return (
    <Card className="w-full mx-auto">
      <CardHeader>
        <CardTitle>Tạo mới</CardTitle>
      </CardHeader>
      <CardContent>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Tên *
            </label>
            <Input
              value={name}
              onChange={(e) => setName(e.target.value)}
              placeholder="Nhập tên..."
              required
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">
              Mô tả
            </label>
            <Textarea
              value={description}
              onChange={(e) => setDescription(e.target.value)}
              placeholder="Nhập mô tả..."
              rows={3}
            />
          </div>

          <div className="flex justify-end gap-2">
            {onCancel && (
              <Button type="button" variant="outline" onClick={onCancel}>
                Hủy
              </Button>
            )}
            <Button
              type="submit"
              disabled={createMutation.isPending || !name.trim()}
            >
              {createMutation.isPending ? "Đang tạo..." : "Tạo mới"}
            </Button>
          </div>
        </form>
      </CardContent>
    </Card>
  );
}
```

#### **4.2 List Component**
```typescript
// src/features/[feature]/components/MyFeatureList.tsx
"use client";
import { Button } from "@/components/ui/button";
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card";
import { useMyFeatures, useDeleteMyFeature } from "../useMyFeatureQuery";

export function MyFeatureList() {
  const { data: items, isLoading, error } = useMyFeatures();
  const deleteMutation = useDeleteMyFeature();

  if (isLoading) {
    return <div className="p-4">Đang tải...</div>;
  }

  if (error) {
    return <div className="p-4 text-red-600">Có lỗi xảy ra: {error.message}</div>;
  }

  if (!items || items.length === 0) {
    return <div className="p-4 text-gray-500">Chưa có dữ liệu</div>;
  }

  return (
    <div className="space-y-4">
      {items.map((item) => (
        <Card key={item.id}>
          <CardHeader>
            <CardTitle className="flex justify-between items-center">
              <span>{item.name}</span>
              <Button
                variant="destructive"
                size="sm"
                onClick={() => deleteMutation.mutate(item.id)}
                disabled={deleteMutation.isPending}
              >
                {deleteMutation.isPending ? "Đang xóa..." : "Xóa"}
              </Button>
            </CardTitle>
          </CardHeader>
          {item.description && (
            <CardContent>
              <p className="text-gray-600">{item.description}</p>
            </CardContent>
          )}
        </Card>
      ))}
    </div>
  );
}
```

#### **4.3 Dialog Component**
```typescript
// src/features/[feature]/components/MyFeatureDialog.tsx
"use client";
import { useState } from "react";
import { Button } from "@/components/ui/button";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "@/components/ui/dialog";
import { MyFeatureForm } from "./MyFeatureForm";

export function MyFeatureDialog() {
  const [open, setOpen] = useState(false);

  const handleSuccess = () => {
    setOpen(false);
  };

  const handleCancel = () => {
    setOpen(false);
  };

  return (
    <>
      <Button onClick={() => setOpen(true)}>
        Tạo mới
      </Button>

      <Dialog open={open} onOpenChange={setOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Tạo mới</DialogTitle>
          </DialogHeader>

          <MyFeatureForm
            onSuccess={handleSuccess}
            onCancel={handleCancel}
          />
        </DialogContent>
      </Dialog>
    </>
  );
}
```

#### **4.4 Manager Component**
```typescript
// src/features/[feature]/components/MyFeatureManager.tsx
import { MyFeatureDialog } from "./MyFeatureDialog";
import { MyFeatureList } from "./MyFeatureList";

export function MyFeatureManager() {
  return (
    <div className="space-y-6">
      {/* Header */}
      <div className="flex justify-between items-center">
        <div>
          <h1 className="text-2xl font-bold">Quản lý My Feature</h1>
          <p className="text-gray-600">Tạo và quản lý các items.</p>
        </div>
        <MyFeatureDialog />
      </div>

      {/* List */}
      <MyFeatureList />
    </div>
  );
}
```

### **Bước 5: Tạo Page (5 phút)**
```typescript
// src/app/my-feature/page.tsx
import { MyFeatureManager } from "@/features/my-feature/components/MyFeatureManager";

export default function MyFeaturePage() {
  return (
    <div className="container mx-auto py-6 px-4">
      <MyFeatureManager />
    </div>
  );
}
```

---

## 📁 Cấu trúc thư mục chuẩn

```
src/
├── shared/types/
│   └── myFeatureTypes.ts          # Types definition
├── services/
│   └── myFeatureService.ts        # API calls & mock data
├── features/my-feature/
│   ├── useMyFeatureQuery.ts       # React Query hooks
│   └── components/
│       ├── MyFeatureForm.tsx      # Form component
│       ├── MyFeatureList.tsx      # List component
│       ├── MyFeatureDialog.tsx    # Dialog wrapper
│       └── MyFeatureManager.tsx   # Main container
└── app/my-feature/
    └── page.tsx                   # Next.js page
```

---

## ✅ Checklist khi tạo feature mới

- [ ] **Types**: Định nghĩa data structure
- [ ] **Service**: Mock data + API functions
- [ ] **Hooks**: React Query setup
- [ ] **Form**: Create/Edit form
- [ ] **List**: Display data
- [ ] **Dialog**: Modal wrapper
- [ ] **Manager**: Container component
- [ ] **Page**: Next.js route
- [ ] **Test**: Tạo thử và kiểm tra

---

## 🎯 Tips quan trọng

### **✅ Làm:**
- Bắt đầu với mock data
- Test từng bước nhỏ
- Copy template và sửa tên
- Dùng TypeScript strict

### **❌ Tránh:**
- Viết quá nhiều code cùng lúc
- Bỏ qua error handling
- Không test trước khi tiếp tục
- Hard-code values

---

## 🚨 Troubleshooting

### **Data không update sau khi create:**
1. Kiểm tra service có tạo ID không
2. Kiểm tra query key có match không
3. Thử dùng `refetchQueries` thay vì `invalidateQueries`

### **Form không reset:**
1. Kiểm tra `onSuccess` callback
2. Đảm bảo `form.reset()` được gọi
3. Check state management

### **Dialog không đóng:**
1. Kiểm tra `onSuccess` callback
2. Verify state management
3. Check error handling

---

**Happy coding! 🚀**
