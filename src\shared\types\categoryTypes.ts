// Base Category type
export type Category = {
  id: string;
  name: string;
  description?: string;
  color: string;
  isActive: boolean;
  order: number;
  createdAt: string;
  updatedAt: string;
  projectId?: string; // Optional nếu category thuộc về project cụ thể
  issueCount?: number; // Số lượng issue sử dụng category này
};

// Category với thông tin thống kê
export type CategoryWithStats = Category & {
  issueCount: number;
  activeIssueCount: number;
  completedIssueCount: number;
};

// Category cho dropdown/select
export type CategoryOption = {
  id: string;
  name: string;
  color: string;
  isActive: boolean;
};

// API Response types
export type CategoriesResponse = {
  data: Category[];
  total: number;
  page: number;
  limit: number;
};

export type CategoryResponse = {
  data: Category;
};

// API Request types
export type CreateCategoryRequest = {
  name: string;
  description?: string;
  color: string;
  isActive: boolean;
  projectId?: string;
};

export type UpdateCategoryRequest = Partial<CreateCategoryRequest> & {
  id: string;
};

export type ReorderCategoryRequest = {
  categories: Array<{
    id: string;
    order: number;
  }>;
};

// Filter và Sort types
export type CategoryFilter = {
  search?: string;
  isActive?: boolean;
  projectId?: string;
};

export type CategorySort = {
  field: 'name' | 'createdAt' | 'updatedAt' | 'order' | 'issueCount';
  direction: 'asc' | 'desc';
};

// Query parameters
export type CategoryQueryParams = {
  page?: number;
  limit?: number;
  filter?: CategoryFilter;
  sort?: CategorySort;
};