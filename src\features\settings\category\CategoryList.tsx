"use client";
import { Category } from "@/shared/types/categoryTypes";
import { useCategoryQuery } from "./useCategoryQuery";

export function CategoryList() {
  const { data: categories, isLoading, error } = useCategoryQuery();
  if (isLoading) return <div>Loading...</div>;
  if (error) return <div>Error: {error.message}</div>;
  return (
    <ul>
      {categories?.map((category: Category) => (
        <li key={category.id}>{category.name}</li>
      ))}
    </ul>
  );
}
