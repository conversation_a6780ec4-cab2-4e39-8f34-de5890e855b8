"use client";
import { <PERSON><PERSON> } from "@/components/ui/button";
import { Checkbox } from "@/components/ui/checkbox";
import {
  Form,
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
  FormDescription,
} from "@/components/ui/form";
import { Input } from "@/components/ui/input";
import { Textarea } from "@/components/ui/textarea";
import { zodResolver } from "@hookform/resolvers/zod";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { Card, CardContent } from "@/components/ui/card";
import { IssueType } from "@/shared/types/issueTypes";
import { useEffect } from "react";
import { useCreateIssueType } from "../useIssueTypeQuery";

const formSchema = z.object({
  name: z.string().min(1, "Tên không được để trống"),
  description: z.string().optional(),
  color: z.string().min(1, "<PERSON><PERSON><PERSON> <PERSON>hông được để trống"),
  active: z.boolean(),
});

interface IssueTypeFormProps {
  issueType?: IssueType;
  onSuccess?: () => void;
  onCancel?: () => void;
}

export function IssueTypeForm({
  onSuccess,
  onCancel,
  issueType,
}: IssueTypeFormProps) {
  const form = useForm<z.infer<typeof formSchema>>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      name: "",
      color: "#3B82F6",
      description: "",
      active: true,
    },
  });
  const createIssueTypeMutation = useCreateIssueType();

  async function onSubmit(values: z.infer<typeof formSchema>) {
    try {

      // Gửi data với đúng structure
      await createIssueTypeMutation.mutateAsync({
        name: values.name,
        description: values.description,
        color: values.color,
        active: values.active,
      });

      console.log("✅ Form submitted successfully");

      // Reset form và đóng dialog
      form.reset();
      onSuccess?.();

    } catch (error) {
      console.error("❌ Error submitting form:", error);
    }
  }

  useEffect(() => {
    if (issueType) {
      form.reset(issueType);
    }
  }, [issueType, form]);

  function onClose() {
    form.reset();
    onCancel?.();
  }

  return (
    <Card className="w-full max-w-2xl mx-auto">
      <CardContent>
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {/* Preview */}
            {form.watch("name") && (
              <div className="p-4 bg-gray-50 rounded-lg">
                <FormLabel className="text-sm font-medium text-gray-700 mb-2 block">
                  Preview
                </FormLabel>
                <div
                  className="inline-flex items-center rounded-full px-2.5 py-0.5 text-xs font-semibold text-white"
                  style={{ backgroundColor: form.watch("color") }}
                >
                  {form.watch("name")}
                </div>
              </div>
            )}

            <FormField
              control={form.control}
              name="name"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Tên loại issue</FormLabel>
                  <FormControl>
                    <Input
                      placeholder="Ví dụ: Bug, Feature, Task..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Tên ngắn gọn để phân loại issue
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Mô tả</FormLabel>
                  <FormControl>
                    <Textarea
                      placeholder="Mô tả chi tiết về loại issue này..."
                      {...field}
                    />
                  </FormControl>
                  <FormDescription>
                    Mô tả giúp người dùng hiểu rõ hơn về loại issue
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="color"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Màu sắc</FormLabel>
                  <FormControl>
                    <div className="flex items-center gap-4">
                      <Input
                        type="color"
                        {...field}
                        className="w-20 h-10 p-1"
                      />
                      <span className="text-sm text-gray-500">
                        {field.value.toUpperCase()}
                      </span>
                    </div>
                  </FormControl>
                  <FormDescription>
                    Chọn màu để phân biệt với các loại issue khác
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />

            <FormField
              control={form.control}
              name="active"
              render={({ field }) => (
                <FormItem className="flex flex-row items-start space-x-3 space-y-0 rounded-md border p-4">
                  <FormControl>
                    <Checkbox
                      checked={field.value}
                      onCheckedChange={field.onChange}
                    />
                  </FormControl>
                  <div className="space-y-1 leading-none">
                    <FormLabel>Kích hoạt</FormLabel>
                    <FormDescription>
                      Loại issue sẽ được hiển thị và có thể sử dụng
                    </FormDescription>
                  </div>
                </FormItem>
              )}
            />

            <div className="flex justify-end space-x-4">
              <Button variant="outline" type="button" onClick={onClose}>
                Hủy
              </Button>
              <Button type="submit">Lưu</Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  );
}
